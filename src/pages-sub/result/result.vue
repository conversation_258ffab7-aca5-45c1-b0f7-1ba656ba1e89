<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "视频详情",
    "navigationBarBackgroundColor": "#FFFFFF",
    "navigationBarTextStyle": "black"
  }
}
</route>

<script setup lang="ts">
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import { onMounted, ref } from 'vue'
import { getSafeAreaStyle } from '@/utils/safeArea'
import 'dayjs/locale/zh-cn'

// 配置 dayjs
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

// 获取安全区域样式
const safeAreaStyle = getSafeAreaStyle()

// 响应式数据
const videoData = ref<any>(null)

// 保存到相册
async function saveToAlbum() {
  if (!videoData.value?.video_url)
    return

  try {
    uni.showToast({
      title: '保存成功',
      icon: 'success',
    })
  }
  catch (error) {
    console.error('保存失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'none',
    })
  }
}

// 复制视频链接
function copyVideoUrl() {
  if (!videoData.value?.video_url)
    return

  uni.setClipboardData({
    data: videoData.value.video_url,
    success: () => {
      uni.showToast({
        title: '链接已复制',
        icon: 'success',
      })
    },
  })
}

// 页面加载时获取视频数据
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options || {}

  if (options.data) {
    // 从URL参数中获取解析结果数据
    try {
      const parsedData = JSON.parse(decodeURIComponent(options.data))
      videoData.value = parsedData
    }
    catch (error) {
      console.error('解析数据失败:', error)
      uni.showToast({
        title: '数据解析失败',
        icon: 'none',
      })
      // 返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  }
  else if (options.id) {
    // 这里应该根据ID从历史记录中获取数据
    uni.showToast({
      title: '功能开发中',
      icon: 'none',
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
  else {
    // 没有数据，返回上一页
    uni.showToast({
      title: '缺少数据',
      icon: 'none',
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
})
</script>

<template>
  <view class="page-container" :style="safeAreaStyle">
    <!-- 视频播放区域 -->
    <view class="video-container">
      <view class="video-wrapper">
        <video
          v-if="videoData?.video_url"
          :src="videoData.video_url"
          :poster="videoData.cover"
          controls
          class="video-player"
          object-fit="cover"
          show-center-play-btn
          show-play-btn
          enable-play-gesture
        />

        <!-- 加载状态 -->
        <view v-else class="video-loading">
          <wd-loading type="circular" size="40px" />
          <text class="loading-text">
            视频加载中...
          </text>
        </view>
      </view>
    </view>
    <view class="mt-6" />
    <!-- 视频信息 -->
    <wd-card class="info-card">
      <view class="video-info-content">
        <!-- 标题 -->
        <view class="info-row">
          <text class="info-label">
            标题:
          </text>
          <text class="info-value">
            {{ videoData?.title }}
          </text>
        </view>

        <!-- 创作者 -->
        <view class="info-row">
          <text class="info-label">
            创作者:
          </text>
          <text class="info-value">
            {{ videoData?.author }}
          </text>
        </view>

        <!-- 点赞数量 -->
        <view v-if="videoData?.like" class="info-row">
          <text class="info-label">
            点赞数量:
          </text>
          <text class="info-value">
            {{ videoData.like }}
          </text>
        </view>

        <!-- 发布时间 -->
        <view v-if="videoData?.time" class="info-row">
          <text class="info-label">
            发布时间:
          </text>
          <text class="info-value">
            {{ dayjs.unix(videoData.time).format('YYYY-MM-DD HH:mm:ss') }}
          </text>
        </view>
      </view>
    </wd-card>

    <!-- 操作按钮 -->
    <wd-card class="action-card">
      <view class="action-buttons-row">
        <wd-button
          type="success"
          size="large"
          :disabled="!videoData?.video_url"
          class="action-btn"
          @click="saveToAlbum"
        >
          <wd-icon name="picture" size="16px" />
          保存到相册
        </wd-button>

        <wd-button
          type="warning"
          size="large"
          :disabled="!videoData?.video_url"
          class="action-btn"
          @click="copyVideoUrl"
        >
          <wd-icon name="copy" size="16px" />
          复制链接
        </wd-button>
      </view>
    </wd-card>
  </view>
</template>

<style scoped>
.page-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 8px;
}

.video-container {
  margin-bottom: 24px;
}

.mt-6 {
  margin-top: 24px;
}

.video-player {
  width: 100%;
  aspect-ratio: 16 / 9;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.video-loading {
  width: 100%;
  aspect-ratio: 16 / 9;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.loading-text {
  margin-top: 12px;
  color: #999;
  font-size: 14px;
}

.info-card {
  margin-bottom: 24px;
}

.video-info-content {
  padding: 16px 0;
}

.info-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  padding: 8px 0;
}

.info-label {
  width: 80px;
  font-size: 14px;
  font-weight: 600;
  color: #666;
  flex-shrink: 0;
  line-height: 1.5;
}

.info-value {
  flex: 1;
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  word-break: break-all;
}

.action-card {
  margin-bottom: 24px;
}

.action-buttons-row {
  display: flex;
  gap: 16px;
  padding: 12px 0;
}

.action-btn {
  flex: 1;
  min-height: 48px;
}
</style>
