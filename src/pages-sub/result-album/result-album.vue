<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "图集详情",
    "navigationBarBackgroundColor": "#FFFFFF",
    "navigationBarTextStyle": "black"
  }
}
</route>

<script setup lang="ts">
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import { computed, onMounted, ref } from 'vue'
import { addDownloadRecord } from '@/utils/historyManager'
import { getSafeAreaStyle } from '@/utils/safeArea'
import 'dayjs/locale/zh-cn'

// 配置 dayjs
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

// 获取安全区域样式
const safeAreaStyle = getSafeAreaStyle()

// 响应式数据
const albumData = ref<any>(null)
const isDownloading = ref(false)
const currentImageIndex = ref(0)

// 计算属性
const totalImages = computed(() => albumData.value?.images?.length || 0)

// 处理图片点击 - 打开图片预览
function handleImageClick(index: number, item?: string) {
  if (!albumData.value?.images?.length)
    return

  uni.previewImage({
    urls: albumData.value.images,
    current: index,
  })
}

// 保存所有图片到相册
async function saveToAlbum() {
  if (!albumData.value?.images?.length)
    return

  try {
    // 保存成功后添加到历史记录
    if (albumData.value) {
      await addDownloadRecord({
        type: 'img',
        title: albumData.value.title,
        author: albumData.value.author,
        cover: albumData.value.images?.[0], // 使用第一张图片作为封面
        images: albumData.value.images,
        imageCount: albumData.value.images?.length,
        originalUrl: albumData.value.originalUrl,
      })
    }

    uni.showToast({
      title: '保存成功',
      icon: 'success',
    })
  }
  catch (error) {
    console.error('保存失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'none',
    })
  }
}

// 复制第一张图片链接
function copyImageUrl() {
  if (!albumData.value?.images?.[0])
    return

  uni.setClipboardData({
    data: albumData.value.images[0],
    success: () => {
      uni.showToast({
        title: '链接已复制',
        icon: 'success',
      })
    },
  })
}

// 页面加载时获取图集数据
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = (currentPage as any).options || {}

  if (options.data) {
    try {
      // 从首页传递过来的真实数据
      const decodedData = decodeURIComponent(options.data)
      const parsedData = JSON.parse(decodedData)
      albumData.value = parsedData
      console.log('图集数据:', parsedData)
    }
    catch (error) {
      console.error('解析图集数据失败:', error)
      uni.showToast({
        title: '数据解析失败',
        icon: 'none',
      })
      // 返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  }
  else if (options.id) {
    // 根据ID获取数据（暂时不实现）
    uni.showToast({
      title: '功能开发中',
      icon: 'none',
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
  else {
    // 没有数据，返回上一页
    uni.showToast({
      title: '缺少必要参数',
      icon: 'none',
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
})
</script>

<template>
  <view class="min-h-screen bg-gray-50 p-2" :style="safeAreaStyle">
    <!-- 图片预览区域 -->
    <view class="mb-6">
      <view class="relative w-full overflow-hidden rounded-3 bg-black shadow-lg" style="height: 640rpx;">
        <wd-swiper
          v-if="albumData?.images?.length"
          class="album-swiper"
          :list="albumData.images"
          autoplay
          :autoplay-interval="3000"
          indicator
          indicator-position="bottom"
          @click="handleImageClick"
        />

        <!-- 加载状态 -->
        <view v-else class="h-full w-full flex flex-col items-center justify-center bg-gray-100">
          <wd-loading type="ring" size="40px" />
          <text class="mt-3 text-sm text-gray-500">
            图片加载中...
          </text>
        </view>

        <!-- 图片计数器 -->
        <view v-if="albumData?.images?.length" class="absolute bottom-3 right-3 rounded-3 bg-black bg-opacity-60 px-2 py-1 text-xs text-white">
          <text>{{ albumData.images.length }} 张图片</text>
        </view>
      </view>
    </view>

    <!-- 图集信息 -->
    <wd-card class="mb-6">
      <view class="py-4">
        <!-- 标题 -->
        <view class="mb-3 flex items-start py-2">
          <text class="w-20 flex-shrink-0 text-sm text-gray-600 font-semibold leading-6">
            标题:
          </text>
          <text class="flex-1 break-all text-sm text-gray-800 leading-6">
            {{ albumData?.title }}
          </text>
        </view>

        <!-- 创作者 -->
        <view class="mb-3 flex items-start py-2">
          <text class="w-20 flex-shrink-0 text-sm text-gray-600 font-semibold leading-6">
            创作者:
          </text>
          <text class="flex-1 break-all text-sm text-gray-800 leading-6">
            {{ albumData?.author }}
          </text>
        </view>

        <!-- 图片数量 -->
        <view v-if="albumData?.count || albumData?.images?.length" class="mb-3 flex items-start py-2">
          <text class="w-20 flex-shrink-0 text-sm text-gray-600 font-semibold leading-6">
            图片数量:
          </text>
          <text class="flex-1 break-all text-sm text-gray-800 leading-6">
            {{ albumData.count || albumData.images.length }} 张
          </text>
        </view>

        <!-- 发布时间 -->
        <view v-if="albumData?.time" class="mb-3 flex items-start py-2">
          <text class="w-20 flex-shrink-0 text-sm text-gray-600 font-semibold leading-6">
            发布时间:
          </text>
          <text class="flex-1 break-all text-sm text-gray-800 leading-6">
            {{
              typeof albumData.time === 'number'
                ? dayjs.unix(albumData.time).format('YYYY-MM-DD HH:mm:ss')
                : albumData.time
            }}
          </text>
        </view>
      </view>
    </wd-card>

    <!-- 操作按钮 -->
    <wd-card class="mb-6">
      <view class="flex gap-4 py-3">
        <wd-button
          type="success"
          size="large"
          :disabled="!albumData?.images?.length"
          class="flex-1"
          style="min-height: 96rpx;"
          @click="saveToAlbum"
        >
          <wd-icon name="picture" size="32rpx" />
          保存到相册
        </wd-button>

        <wd-button
          type="warning"
          size="large"
          :disabled="!albumData?.images?.length"
          class="flex-1"
          style="min-height: 96rpx;"
          @click="copyImageUrl"
        >
          <wd-icon name="copy" size="32rpx" />
          复制链接
        </wd-button>
      </view>
    </wd-card>

    <!-- 底部间距 -->
    <view style="margin-bottom: 160rpx;" />
  </view>
</template>

<style scoped>
.album-swiper {
  width: 100%;
  height: 100%;
  border-radius: 24rpx;
  overflow: hidden;
}

/* wd-swiper 容器样式 */
.album-swiper :deep(.wd-swiper) {
  width: 100%;
  height: 100%;
  border-radius: 24rpx;
  overflow: hidden;
  background-color: #000;
}

/* wd-swiper 轮播项容器 */
.album-swiper :deep(.wd-swiper__wrapper) {
  background-color: #000;
}

/* wd-swiper 每个轮播项 */
.album-swiper :deep(.wd-swiper__item) {
  background-color: #000 !important;
  padding: 0 !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* wd-swiper 内部图片组件 */
.album-swiper :deep(.wd-img) {
  width: 100% !important;
  height: 100% !important;
  background-color: #000 !important;
}

.album-swiper :deep(.wd-img__inner) {
  width: 100% !important;
  height: 100% !important;
  background-color: #000 !important;
}

/* 原生 image 标签 */
.album-swiper :deep(image) {
  width: 100% !important;
  height: 100% !important;
  object-fit: contain !important;
  background-color: #000 !important;
}

/* 轮播指示器样式 */
.album-swiper :deep(.wd-swiper__indicators) {
  bottom: 16rpx;
}

.album-swiper :deep(.wd-swiper__indicator) {
  background-color: rgba(255, 255, 255, 0.4);
}

.album-swiper :deep(.wd-swiper__indicator--active) {
  background-color: #fff;
}
</style>
