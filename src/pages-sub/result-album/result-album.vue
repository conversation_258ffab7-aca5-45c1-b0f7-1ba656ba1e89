<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "图集详情",
    "navigationBarBackgroundColor": "#FFFFFF",
    "navigationBarTextStyle": "black"
  }
}
</route>

<script setup lang="ts">
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import { computed, onMounted, ref } from 'vue'
import { getSafeAreaStyle } from '@/utils/safeArea'
import 'dayjs/locale/zh-cn'

// 配置 dayjs
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

// 获取安全区域样式
const safeAreaStyle = getSafeAreaStyle()

// 响应式数据
const albumData = ref<any>(null)
const isDownloading = ref(false)
const currentIndex = ref(0)

// 计算属性
const totalImages = computed(() => albumData.value?.images?.length || 0)

// 处理图片点击 - 打开图片预览
function handleImageClick(index: number, item?: string) {
  if (!albumData.value?.images?.length)
    return

  uni.previewImage({
    urls: albumData.value.images,
    current: index,
  })
}

// 保存所有图片到相册
async function saveToAlbum() {
  if (!albumData.value?.images?.length) {
    uni.showToast({
      title: '没有图片可保存',
      icon: 'none',
    })
    return
  }

  isDownloading.value = true

  try {
    let successCount = 0
    let failCount = 0
    const total = albumData.value.images.length

    // 显示保存进度
    uni.showLoading({
      title: `保存中 0/${total}`,
    })

    // 逐一保存每张图片
    for (let i = 0; i < albumData.value.images.length; i++) {
      const imageUrl = albumData.value.images[i]

      try {
        // 使用uni.saveImageToPhotosAlbum保存图片到相册
        await new Promise<void>((resolve, reject) => {
          uni.saveImageToPhotosAlbum({
            filePath: imageUrl,
            success: () => {
              successCount++
              uni.showLoading({
                title: `保存中 ${successCount + failCount}/${total}`,
              })
              resolve()
            },
            fail: (error) => {
              console.error(`保存第${i + 1}张图片失败:`, error)
              failCount++
              uni.showLoading({
                title: `保存中 ${successCount + failCount}/${total}`,
              })
              resolve() // 即使失败也继续下一张
            },
          })
        })
      }
      catch (error) {
        console.error(`保存第${i + 1}张图片异常:`, error)
        failCount++
      }
    }

    // 隐藏加载框
    uni.hideLoading()

    // 显示保存结果
    if (successCount === total) {
      uni.showToast({
        title: `成功保存${successCount}张图片`,
        icon: 'success',
        duration: 2000,
      })
    }
    else if (successCount > 0) {
      uni.showToast({
        title: `成功保存${successCount}张，失败${failCount}张`,
        icon: 'none',
        duration: 2000,
      })
    }
    else {
      uni.showToast({
        title: '保存失败，请检查权限',
        icon: 'none',
        duration: 2000,
      })
    }
  }
  catch (error) {
    console.error('保存图片到相册失败:', error)
    uni.hideLoading()
    uni.showToast({
      title: '保存失败',
      icon: 'none',
      duration: 2000,
    })
  }
  finally {
    isDownloading.value = false
  }
}

// 复制当前显示的图片链接
function copyImageUrl() {
  if (!albumData.value?.images?.length) {
    uni.showToast({
      title: '没有图片可复制',
      icon: 'none',
    })
    return
  }

  // 使用双向绑定的当前索引
  const currentImage = albumData.value.images[currentIndex.value]

  if (!currentImage) {
    uni.showToast({
      title: '当前图片不存在',
      icon: 'none',
    })
    return
  }

  uni.setClipboardData({
    data: currentImage,
    success: () => {
      uni.showToast({
        title: '当前图片链接已复制',
        icon: 'success',
      })
    },
    fail: (error) => {
      console.error('复制失败:', error)
      uni.showToast({
        title: '复制失败',
        icon: 'none',
      })
    },
  })
}

// 页面加载时获取图集数据
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = (currentPage as any).options || {}

  if (options.data) {
    try {
      // 从首页传递过来的真实数据
      const decodedData = decodeURIComponent(options.data)
      const parsedData = JSON.parse(decodedData)
      albumData.value = parsedData
      console.log('图集数据:', parsedData)
    }
    catch (error) {
      console.error('解析图集数据失败:', error)
      uni.showToast({
        title: '数据解析失败',
        icon: 'none',
      })
      // 返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  }
  else if (options.id) {
    // 根据ID获取数据（暂时不实现）
    uni.showToast({
      title: '功能开发中',
      icon: 'none',
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
  else {
    // 没有数据，返回上一页
    uni.showToast({
      title: '缺少必要参数',
      icon: 'none',
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
})
</script>

<template>
  <view class="min-h-screen bg-gray-50 p-2" :style="safeAreaStyle">
    <!-- 图片预览区域 -->
    <view class="mb-6">
      <view class="relative w-full overflow-hidden rounded-3 shadow-lg" style="height: 640rpx;">
        <wd-swiper
          v-if="albumData?.images?.length"
          v-model:current="currentIndex"
          class="album-swiper"
          :list="albumData.images"
          :autoplay="true"
          :autoplay-interval="3000"
          :indicator="{ type: 'fraction' } as any"
          indicator-position="bottom-right"
          height="640rpx"
          custom-item-class="custom-swiper-item"
          image-mode="aspectFill"
          @click="handleImageClick"
        />

        <!-- 加载状态 -->
        <view v-else class="h-full w-full flex flex-col items-center justify-center bg-gray-100">
          <wd-loading type="ring" size="40px" />
          <text class="mt-3 text-sm text-gray-500">
            图片加载中...
          </text>
        </view>
      </view>
    </view>

    <!-- 图集信息 -->
    <wd-card class="mb-6">
      <view class="py-4">
        <!-- 标题 -->
        <view class="mb-3 flex items-start py-2">
          <text class="w-20 flex-shrink-0 text-sm text-gray-600 font-semibold leading-6">
            标题:
          </text>
          <text class="flex-1 break-all text-sm text-gray-800 leading-6">
            {{ albumData?.title }}
          </text>
        </view>

        <!-- 创作者 -->
        <view class="mb-3 flex items-start py-2">
          <text class="w-20 flex-shrink-0 text-sm text-gray-600 font-semibold leading-6">
            创作者:
          </text>
          <text class="flex-1 break-all text-sm text-gray-800 leading-6">
            {{ albumData?.author }}
          </text>
        </view>

        <!-- 图片数量 -->
        <view v-if="albumData?.count || albumData?.images?.length" class="mb-3 flex items-start py-2">
          <text class="w-20 flex-shrink-0 text-sm text-gray-600 font-semibold leading-6">
            图片数量:
          </text>
          <text class="flex-1 break-all text-sm text-gray-800 leading-6">
            {{ albumData.count || albumData.images.length }} 张
          </text>
        </view>

        <!-- 发布时间 -->
        <view v-if="albumData?.time" class="mb-3 flex items-start py-2">
          <text class="w-20 flex-shrink-0 text-sm text-gray-600 font-semibold leading-6">
            发布时间:
          </text>
          <text class="flex-1 break-all text-sm text-gray-800 leading-6">
            {{
              typeof albumData.time === 'number'
                ? dayjs.unix(albumData.time).format('YYYY-MM-DD HH:mm:ss')
                : albumData.time
            }}
          </text>
        </view>
      </view>
    </wd-card>

    <!-- 操作按钮 -->
    <wd-card class="mb-6">
      <view class="flex gap-4 py-3">
        <wd-button
          type="success"
          size="large"
          :disabled="!albumData?.images?.length"
          class="flex-1"
          style="min-height: 96rpx;"
          @click="saveToAlbum"
        >
          <wd-icon name="picture" size="32rpx" />
          保存到相册
        </wd-button>

        <wd-button
          type="warning"
          size="large"
          :disabled="!albumData?.images?.length"
          class="flex-1"
          style="min-height: 96rpx;"
          @click="copyImageUrl"
        >
          <wd-icon name="copy" size="32rpx" />
          复制链接
        </wd-button>
      </view>
    </wd-card>

    <!-- 底部间距 -->
    <view style="margin-bottom: 160rpx;" />
  </view>
</template>

<style scoped>
/* 确保轮播组件占满容器 */
.album-swiper {
  width: 100% !important;
  height: 100% !important;
  border-radius: 24rpx;
  overflow: hidden;
}

/* 使用组件提供的自定义样式类 */
.album-swiper :deep(.custom-swiper-item) {
  width: 100% !important;
  height: 100% !important;
}
</style>
