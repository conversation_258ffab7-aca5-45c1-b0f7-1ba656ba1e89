import type { XiaohongshuData, XiaohongshuParseParams, XiaohongshuRawResponse } from './types/xiaohongshu'
import { http } from '@/http/http'

/**
 * 小红书视频/图集解析
 * 按照抖音接口格式返回统一的数据结构
 */
export async function parseXiaohongshu(params: XiaohongshuParseParams): Promise<XiaohongshuData> {
  // 使用项目封装的http请求库
  const response = await http.get<XiaohongshuRawResponse['data']>('/vip/video', {
    url: params.url,
  })

  const rawData = response.data

  // 转换为统一格式（与抖音接口保持一致）
  const transformedData: XiaohongshuData = {
    title: rawData.title,
    author: rawData.author,
    like: rawData.like || '0',
    time: rawData.time || rawData.text.time,
  }

  // 判断是视频还是图集
  if (rawData.url && rawData.cover) {
    // 视频类型
    transformedData.video_url = rawData.url
    transformedData.cover = rawData.cover
  }
  else if (rawData.images && Array.isArray(rawData.images)) {
    // 图集类型
    transformedData.images = rawData.images
    transformedData.count = rawData.images.length
  }
  else {
    throw new Error('无法识别内容类型')
  }

  return transformedData
}
