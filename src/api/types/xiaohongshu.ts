/**
 * 小红书API相关类型定义
 * 按照抖音接口格式统一设计
 */

// 小红书内容数据（统一格式，与抖音接口保持一致）
export interface XiaohongshuData {
  title: string
  author: string
  like: string | number // 支持字符串或数字
  time: string | number // 支持字符串或数字
  // 视频相关字段（可选）
  video_url?: string
  cover?: string
  // 图集相关字段（可选）
  count?: number
  images?: string[]
}

// 小红书API原始响应数据格式（服务端data字段的格式）
export interface XiaohongshuRawResponse {
  data: {
    author: string
    // 视频字段
    cover?: string
    like?: string
    url?: string
    // 图集字段
    images?: string[]
    // 公共字段
    text: {
      msg: string
      time: string
    }
    time?: string
    title: string
  }
}

// 小红书解析请求参数
export interface XiaohongshuParseParams {
  url: string
}

// 内容类型枚举
export enum XiaohongshuContentType {
  VIDEO = 'video',
  IMAGE = 'image',
}

// 类型守卫函数
export function isXiaohongshuVideo(data: XiaohongshuData): data is XiaohongshuData & { video_url: string } {
  return !!data.video_url
}

export function isXiaohongshuImage(data: XiaohongshuData): data is XiaohongshuData & { images: string[], count: number } {
  return !!data.images && !!data.count
}

// 获取内容类型的工具函数
export function getXiaohongshuContentType(data: XiaohongshuData): XiaohongshuContentType {
  return isXiaohongshuVideo(data) ? XiaohongshuContentType.VIDEO : XiaohongshuContentType.IMAGE
}
