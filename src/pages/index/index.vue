<route lang="jsonc" type="home">
{
  "layout": "tabbar",
  "style": {
    "navigationBarTitleText": "趣水印",
    "navigationBarBackgroundColor": "#FFFFFF",
    "navigationBarTextStyle": "black"
  }
}
</route>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { parseDouyin } from '@/api/douyin'
import { parseXiaohongshu } from '@/api/xiaohongshu'
import { getRecentRecords } from '@/utils/historyManager'
import { getSafeAreaStyle } from '@/utils/safeArea'

defineOptions({
  name: 'Home',
})

// 获取安全区域样式
const safeAreaStyle = getSafeAreaStyle()

// 响应式数据
const linkInput = ref('')
const isLoading = ref(false)
const recentHistory = ref<any[]>([])

// 处理输入框内容变化，自动提取链接
function handleInputChange(value: string) {
  if (value && value.length > 20) { // 只有当输入内容较长时才尝试提取
    const extractedUrl = extractUrl(value)
    if (extractedUrl && extractedUrl !== value) {
      // 如果提取到的链接与原内容不同，说明原内容包含中文，自动替换
      linkInput.value = extractedUrl
    }
  }
}

// 加载最近下载记录
async function loadRecentHistory() {
  try {
    recentHistory.value = await getRecentRecords(3)
  }
  catch (error) {
    console.error('加载最近记录失败:', error)
    recentHistory.value = []
  }
}

// 支持的平台 - 使用确实存在的Carbon图标
const supportedPlatforms = [
  { icon: 'i-carbon-music', bgColor: 'bg-black' }, // 抖音 - 音乐图标
  { icon: 'i-carbon-fire', bgColor: 'bg-red-500' }, // 快手 - 火焰图标
  { icon: 'i-carbon-book', bgColor: 'bg-pink-500' }, // 小红书 - 书本图标
  { icon: 'i-carbon-share', bgColor: 'bg-blue-500' }, // 微博 - 分享图标
  { icon: 'i-carbon-document', bgColor: 'bg-red-600' }, // 今日头条 - 文档图标
  { icon: 'i-carbon-video', bgColor: 'bg-green-500' }, // 西瓜视频 - 视频图标
  { icon: 'i-carbon-play', bgColor: 'bg-purple-500' }, // B站 - 播放图标
  { icon: 'i-carbon-menu', bgColor: 'bg-gray-400' }, // 更多 - 菜单图标
]

// 提取文本中的URL，使用通用URL匹配
function extractUrl(text: string): string | null {
  // 通用URL匹配模式
  const urlPattern = /https?:\/\/[^\s\u4E00-\u9FFF，。！？；："'（）【】《》]+/gi

  const matches = text.match(urlPattern)
  if (matches && matches.length > 0) {
    // 清理链接，移除末尾可能的标点符号
    const url = matches[0].replace(/[，。！？；："'（）【】《》\s]*$/, '')
    return url
  }

  return null
}

// 检测平台类型
function detectPlatform(url: string): 'douyin' | 'xiaohongshu' | 'unknown' {
  if (/douyin\.com|iesdouyin\.com|v\.douyin\.com/i.test(url)) {
    return 'douyin'
  }
  if (/xiaohongshu\.com|xhslink\.com/i.test(url)) {
    return 'xiaohongshu'
  }
  return 'unknown'
}

// 从剪贴板粘贴
function pasteFromClipboard() {
  uni.getClipboardData({
    success: (res) => {
      if (res.data && res.data.trim()) {
        const clipboardText = res.data.trim()

        // 尝试提取纯净的URL链接
        const extractedUrl = extractUrl(clipboardText)

        if (extractedUrl) {
          // 只在输入框显示提取出的纯净链接，过滤掉所有中文内容
          linkInput.value = extractedUrl
          uni.showToast({
            title: '链接提取成功',
            icon: 'success',
            duration: 1500,
          })
        }
        else {
          // 如果没有找到有效链接，不填充输入框，提示用户
          uni.showToast({
            title: '未找到有效链接，请检查内容',
            icon: 'none',
            duration: 2000,
          })
        }
      }
      else {
        uni.showToast({
          title: '剪贴板为空',
          icon: 'none',
          duration: 1500,
        })
      }
    },
    fail: () => {
      uni.showToast({
        title: '获取剪贴板失败',
        icon: 'none',
        duration: 1500,
      })
    },
  })
}

// 处理解析
async function handleExtract() {
  if (!linkInput.value.trim()) {
    uni.showToast({
      title: '请输入链接',
      icon: 'none',
    })
    return
  }

  const url = linkInput.value.trim()
  isLoading.value = true

  try {
    let result: any
    const platform = detectPlatform(url)

    if (platform === 'douyin') {
      // 调用抖音接口
      const douyinData = await parseDouyin({ url })

      // 检查API返回数据是否有效
      if (!douyinData) {
        throw new Error('解析失败，未获取到有效数据')
      }

      // 直接使用API返回的数据，只添加必要的字段
      result = {
        ...douyinData,
        type: douyinData.video_url ? 'video' : 'img',
        originalUrl: url,
      }
    }
    else if (platform === 'xiaohongshu') {
      // 调用小红书接口
      const xiaohongshuData = await parseXiaohongshu({ url })

      // 检查API返回数据是否有效
      if (!xiaohongshuData) {
        throw new Error('解析失败，未获取到有效数据')
      }

      // 数据格式已经统一，直接使用并添加必要字段
      result = {
        ...xiaohongshuData,
        type: xiaohongshuData.video_url ? 'video' : 'img',
        originalUrl: url,
      }
    }
    else {
      // 不支持的平台
      uni.showToast({
        title: '暂不支持该平台',
        icon: 'none',
      })
      return
    }

    // 跳转到结果页面（分包路径）
    const resultUrl = result.type === 'video'
      ? `/pages-sub/result/result?data=${encodeURIComponent(JSON.stringify(result))}`
      : `/pages-sub/result-album/result-album?data=${encodeURIComponent(JSON.stringify(result))}`

    uni.navigateTo({
      url: resultUrl,
      success: () => {
        console.log('跳转成功')
      },
      fail: (err) => {
        console.error('跳转失败:', err)
        uni.showToast({
          title: '页面跳转失败',
          icon: 'none',
        })
      },
    })

    // 清空输入框
    linkInput.value = ''
  }
  catch (error) {
    console.error('解析失败:', error)
    uni.showToast({
      title: error instanceof Error ? error.message : '解析失败，请检查链接是否正确',
      icon: 'none',
    })
  }
  finally {
    isLoading.value = false
  }
}

// 导航到历史记录
function navigateToHistory() {
  uni.navigateTo({
    url: '/pages-sub/history/history',
    success: () => {
      console.log('跳转到历史记录成功')
    },
    fail: (err) => {
      console.error('跳转失败:', err)
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none',
      })
    },
  })
}

// 导航到内容详情
function navigateToContent(item: any) {
  const url = item.type === 'video'
    ? `/pages-sub/result/result?id=${item.id}`
    : `/pages-sub/result-album/result-album?id=${item.id}`

  uni.navigateTo({
    url,
    success: () => {
      console.log('跳转到内容详情成功')
    },
    fail: (err) => {
      console.error('跳转失败:', err)
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none',
      })
    },
  })
}

// 页面加载时初始化
onMounted(() => {
  // 加载最近下载记录
  loadRecentHistory()
})
</script>

<template>
  <view class="mx-auto min-h-screen bg-gray-50 px-4" :style="safeAreaStyle">
    <!-- 链接输入区域 -->
    <wd-card title="粘贴链接获取内容">
      <view class="relative">
        <textarea
          v-model="linkInput" placeholder="请粘贴视频或图集分享链接..."
          class="min-h-24 w-full resize-none border border-gray-200 rounded-xl bg-gray-50 p-4 text-sm" :maxlength="500"
          @input="handleInputChange(linkInput)"
        />
        <view class="absolute bottom-2 right-2" style="z-index: 10;">
          <wd-button type="text" size="small" custom-style="pointer-events: auto;" @click="pasteFromClipboard">
            粘贴
          </wd-button>
        </view>
      </view>

      <wd-button
        type="primary" block size="large" :loading="isLoading" :disabled="!linkInput.trim()" class="mb-3 mt-4"
        @click="handleExtract"
      >
        {{ isLoading ? '正在提取中...' : '一键提取无水印内容' }}
      </wd-button>

      <text class="block text-center text-xs text-gray-500">
        支持各大平台视频及图集，无需登录
      </text>
    </wd-card>

    <!-- 区域间隔 -->
    <view class="mb-8" />

    <!-- 平台支持 -->
    <wd-card title="支持平台">
      <view class="platform-grid">
        <view v-for="(platform, index) in supportedPlatforms" :key="index" class="platform-item">
          <view class="platform-icon" :class="[platform.bgColor]">
            <text :class="platform.icon" class="text-white" />
          </view>
        </view>
      </view>
    </wd-card>

    <!-- 区域间隔 -->
    <view class="mb-8" />

    <!-- 最近下载 -->
    <wd-card>
      <template #title>
        <view class="flex items-center justify-between">
          <text>最近下载</text>
          <wd-button type="text" size="small" @click="navigateToHistory">
            查看全部
          </wd-button>
        </view>
      </template>

      <view v-if="recentHistory.length === 0" class="py-8 text-center">
        <text class="text-gray-400">
          暂无下载记录
        </text>
      </view>

      <view v-else>
        <wd-cell
          v-for="(item, index) in recentHistory.slice(0, 3)" :key="index" :title="item.title || '未知标题'"
          :label="item.time" is-link @click="navigateToContent(item)"
        >
          <template #icon>
            <view class="relative mr-3 h-12 w-12 overflow-hidden rounded-lg bg-gray-100">
              <image v-if="item.cover" :src="item.cover" class="h-full w-full object-cover" />
              <view class="absolute right-0 top-0 p-1">
                <text class="text-xs text-white" :class="item.type === 'video' ? 'i-carbon-play' : 'i-carbon-image'" />
              </view>
            </view>
          </template>
        </wd-cell>
      </view>
    </wd-card>

    <!-- 底部间距 -->
    <view class="mb-20" />
  </view>
</template>

<style scoped>
.min-h-24 {
  min-height: 6rem;
}

/* 平台网格布局 */
.platform-grid {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}

.platform-item {
  width: 25%;
  box-sizing: border-box;
  padding: 0 4px;
  margin-bottom: 12px;
  display: flex;
  justify-content: center;
}

.platform-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
